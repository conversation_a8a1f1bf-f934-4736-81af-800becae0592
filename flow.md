    Saat GPT mengakses API kamu, token yang dikirim harus bisa memberitahu:
    “Ini token dari client ID 12, untuk GPT ID 5 — status: aktif”

✅ 1. Saat Client Di-Authorize

Saat client login via GET /oauth/authorize?gpt_id=5, simpan gpt_id dari URL.
✅ 2. Saat Token Di-Issue (POST /oauth/token)

Generate token dengan payload seperti ini (pakai JWT):

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

$payload = [
  'client_id' => $client->id,
  'gpt_id' => $request->input('gpt_id'),
  'exp' => strtotime($clientGptAccess->expired_at),
];

$token = JWT::encode($payload, env('JWT_SECRET'), 'HS256');

Simpan juga ke DB client_gpt_access:

ClientGptAccess::create([
  'client_id' => $client->id,
  'gpt_id' => $gptId,
  'token' => $token,
  'status' => 'active',
  'expired_at' => $client->expired_at,
]);

✅ 3. Endpoint /client/me

Saat GPT mengakses:

GET /client/me
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5...

Kamu decode token:

try {
    $decoded = JWT::decode($token, new Key(env('JWT_SECRET'), 'HS256'));
    $clientId = $decoded->client_id;
    $gptId = $decoded->gpt_id;
} catch (Exception $e) {
    return response()->json(['error' => 'Invalid token'], 401);
}

Lalu cek di DB:

$access = ClientGptAccess::where('client_id', $clientId)
    ->where('gpt_id', $gptId)
    ->where('token', $token)
    ->where('status', 'active')
    ->where('expired_at', '>', now())
    ->first();

Jika valid:

return response()->json([
  'client_id' => $clientId,
  'gpt_id' => $gptId,
  'can_use_gpt' => true,
  'status' => 'active',
  'expired_at' => $access->expired_at,
]);

Jika tidak:

return response()->json(['can_use_gpt' => false], 403);

🧩 Ringkasan Komponen:
Komponen	Fungsi
gpt_id di URL authorize	Menandai GPT yang ingin diakses
Token JWT	Menyimpan client_id, gpt_id, expired_at
Table client_gpt_access	Validasi token di server
/client/me	Validasi token dan akses

Kalau kamu pakai Laravel Passport atau Sanctum, bisa override token generation-nya atau tambahkan JWT untuk custom access token.