<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\GptController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ClientGptAccessController;
use App\Http\Controllers\OAuthController;

Route::get('/', function () {
    return view('welcome');
});

// Registration Routes
Route::get('register', [RegisterController::class, 'create'])->name('register');
Route::post('register', [RegisterController::class, 'store']);

// Login Routes
Route::get('login', [LoginController::class, 'create'])->name('login');
Route::post('login', [LoginController::class, 'store']);

// Logout Route
Route::post('logout', [LoginController::class, 'destroy'])->name('logout');

// Dashboard Route
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', DashboardController::class)->name('dashboard');

    Route::resource('gpts', GptController::class);
    Route::resource('clients', ClientController::class);
    Route::resource('client-gpt-access', ClientGptAccessController::class)->except(['show']);

    Route::get('/gpt/{id}/ai-plugin.json', [GptController::class, 'aiPluginJson'])->name('gpts.ai-plugin');
    Route::get('/gpt/{id}/openapi.yaml', [GptController::class, 'openapiYaml'])->name('gpts.openapi');
});

// OAuth Routes - Let Passport handle the OAuth flow
// We'll use middleware to capture gpt_id parameter
