<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Laravel\Passport\ClientRepository;
use App\Models\Gpt;
use App\Models\ClientGptAccess;
use Illuminate\Support\Facades\Auth;

class OAuthController extends Controller
{
    /**
     * Show the authorization form.
     */
    public function authorizeForm(Request $request)
    {
        $gptId = $request->get('gpt_id');
        $clientId = $request->get('client_id');

        if (!$gptId || !Gpt::find($gptId)) {
            abort(404, 'GPT not found or gpt_id is missing.');
        }

        if (!$clientId) {
            abort(400, 'Client ID is required.');
        }

        $clientRepository = app(ClientRepository::class);
        $client = $clientRepository->find($clientId);

        if (!$client) {
            abort(404, 'Client not found.');
        }

        // Store it in the session to be used later when issuing the token
        session(['oauth_gpt_id' => $gptId]);

        $gpt = Gpt::find($gptId);

        return view('oauth.authorize', [
            'client' => $client,
            'gpt' => $gpt,
            'request' => $request,
        ]);
    }

    /**
     * Approve the authorization request.
     */
    public function approve(Request $request)
    {
        $request->validate([
            'client_id' => 'required',
            'authorization' => 'required|in:approve,deny',
        ]);

        if ($request->authorization === 'approve') {
            // For now, just redirect to a success page
            // In a full implementation, this would handle the OAuth flow
            return redirect()->route('dashboard')->with('success', 'Authorization approved');
        }

        return redirect()->route('dashboard')->with('error', 'Authorization denied');
    }
}