<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Passport\Http\Controllers\AuthorizationController;
use Psr\Http\Message\ServerRequestInterface;
use Laravel\Passport\ClientRepository;
use App\Models\Gpt;
use App\Models\ClientGptAccess;
use App\Helpers\TokenHelper;
use Illuminate\Support\Facades\Auth;
use <PERSON>cobu<PERSON>\JWT\Configuration;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key\InMemory;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Rsa\Sha256;

class OAuthController extends AuthorizationController
{
    /**
     * Override the default token issuance logic.
     */
    protected function issueToken(ServerRequestInterface $request, $client, $user)
    {
        $response = parent::issueToken($request, $client, $user);

        if ($response->getStatusCode() == 200) {
            $data = json_decode($response->getContent(), true);
            $gptId = session()->pull('oauth_gpt_id');
            $clientModel = Auth::user()->clients()->first(); // Assuming the logged-in user is the client

            if ($gptId && $clientModel) {
                ClientGptAccess::updateOrCreate(
                    [
                        'client_id' => $clientModel->id,
                        'gpt_id' => $gptId,
                    ],
                    [
                        'token' => $data['access_token'],
                        'status' => 'active',
                        'expired_at' => now()->addSeconds($data['expires_in']),
                    ]
                );
            }
        }

        return $response;
    }
    /**
     * Show the authorization form.
     *
     * @param  \Psr\Http\Message\ServerRequestInterface  $request
     * @return \Illuminate\View\View
     */
    public function authorizeForm(ServerRequestInterface $request)
    {
        $clientRepository = app(ClientRepository::class);
        $client = $clientRepository->find($this->getAuthorizationRequest($request)->getClient()->getIdentifier());

        $queryParams = $request->getQueryParams();
        $gptId = $queryParams['gpt_id'] ?? null;

        if (!$gptId || !Gpt::find($gptId)) {
            abort(404, 'GPT not found or gpt_id is missing.');
        }

        // Store it in the session to be used later when issuing the token
        session(['oauth_gpt_id' => $gptId]);

        $gpt = Gpt::find($gptId);

        return view('oauth.authorize', [
            'client' => $client,
            'request' => $request,
            'gpt' => $gpt,
        ]);
    }

    /**
     * Approve the authorization request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function approve(Request $request)
    {
        $this->validate($request, [
            'state' => 'required',
            'client_id' => 'required',
            'authorization' => 'required|in:approve,deny',
        ]);

        $authRequest = $this->getAuthorizationRequest(app(ServerRequestInterface::class));

        if ($request->authorization === 'approve') {
            return $this->approveRequest($authRequest, $request->user());
        }

        return $this->denyRequest($authRequest, $request->user());
    }
}