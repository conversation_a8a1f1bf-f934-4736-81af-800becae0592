<?php

namespace App\Http\Controllers;

use App\Models\Gpt;
use App\Models\GptUsageLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class GptController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Auth::user()->gpts();

        if ($request->has('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $gpts = $query->paginate(10);

        return view('gpts.index', compact('gpts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('gpts.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $gpt = Auth::user()->gpts()->create([
            'name' => $request->name,
            'slug' => $this->generateUniqueSlug($request->name),
            'description' => $request->description,
        ]);

        // Create a Passport client for the new GPT
        $clientName = "GPT: {$gpt->name}";
        Artisan::call('passport:client', [
            '--name' => $clientName,
            '--redirect_uri' => 'https://yourdomain.com/oauth/callback',
            '--user_id' => Auth::id(),
            '--no-interaction' => true,
        ]);

        $output = Artisan::output();
        $clientId = Str::between($output, 'Client ID:', "\n");
        $clientSecret = Str::between($output, 'Client secret:', "\n");

        $gpt->oauthClient()->create([
            'client_id' => trim($clientId),
            'client_secret' => trim($clientSecret),
        ]);

        return redirect()->route('gpts.index')->with('success', 'GPT created successfully.');
    }

/**
     * Display the specified resource.
     */
    public function show(Gpt $gpt)
    {
        if ($gpt->user_id !== Auth::id()) {
            abort(403);
        }

        $usageData = GptUsageLog::whereHas('clientGptAccess', function ($query) use ($gpt) {
                $query->where('gpt_id', $gpt->id);
            })
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date', 'ASC')
            ->pluck('count', 'date');

        $chartLabels = [];
        $chartData = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i)->format('Y-m-d');
            $chartLabels[] = Carbon::parse($date)->format('M d');
            $chartData[] = $usageData[$date] ?? 0;
        }

        return view('gpts.show', compact('gpt', 'chartLabels', 'chartData'));
    }
    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Gpt $gpt)
    {
        if ($gpt->user_id !== Auth::id()) {
            abort(403);
        }
        return view('gpts.edit', compact('gpt'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Gpt $gpt)
    {
        if ($gpt->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $updateData = [
            'name' => $request->name,
            'description' => $request->description,
        ];

        // If the name changed, generate a new unique slug
        if ($gpt->name !== $request->name) {
            $updateData['slug'] = $this->generateUniqueSlug($request->name, $gpt->id);
        }

        $gpt->update($updateData);

        return redirect()->route('gpts.index')->with('success', 'GPT updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Gpt $gpt)
    {
        if ($gpt->user_id !== Auth::id()) {
            abort(403);
        }

        $gpt->delete();

        return redirect()->route('gpts.index')->with('success', 'GPT deleted successfully.');
    }

    public function aiPluginJson(Gpt $gpt)
    {
        if ($gpt->user_id !== Auth::id()) {
            abort(403);
        }

        return response()->json([
            'schema_version' => 'v1',
            'name_for_human' => $gpt->name,
            'name_for_model' => Str::snake($gpt->name),
            'description_for_human' => $gpt->description,
            'description_for_model' => $gpt->description,
            'auth' => [
                'type' => 'oauth',
                'client_url' => route('passport.authorizations.authorize'),
                'scope' => 'gptaccess',
                'authorization_url' => route('gpts.openapi', $gpt),
                'authorization_content_type' => 'application/json',
            ],
            'api' => [
                'type' => 'openapi',
                'url' => route('gpts.openapi', $gpt),
                'is_user_authenticated' => false,
            ],
            'logo_url' => url('/logo.png'),
            'contact_email' => $gpt->user->email,
            'legal_info_url' => url('/legal'),
        ]);
    }

    public function openapiYaml(Gpt $gpt)
    {
        if ($gpt->user_id !== Auth::id()) {
            abort(403);
        }

        $yaml = <<<EOT
openapi: 3.0.1
info:
  title: {$gpt->name}
  description: {$gpt->description}
  version: 'v1'
servers:
  - url: {{config('app.url')}}
paths:
  /api/v1/gpts/{$gpt->slug}/access:
    get:
      operationId: checkAccess
      summary: Check if a client has access to this GPT
      parameters:
        - in: query
          name: client_id
          schema:
            type: string
          required: true
          description: The client's unique identifier.
      responses:
        '200':
          description: Access status
          content:
            application/json:
              schema:
                type: object
                properties:
                  has_access:
                    type: boolean
EOT;

        return response($yaml, 200, [
            'Content-Type' => 'text/yaml',
        ]);
    }

    /**
     * Generate a unique slug for a GPT name.
     */
    private function generateUniqueSlug(string $name, ?int $excludeId = null): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        $query = Gpt::where('slug', $slug);
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        while ($query->exists()) {
            $counter++;
            $slug = $baseSlug . '-' . $counter;
            $query = Gpt::where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
        }

        return $slug;
    }
}
