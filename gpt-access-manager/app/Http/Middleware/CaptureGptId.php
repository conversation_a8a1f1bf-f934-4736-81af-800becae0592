<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CaptureGptId
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Capture gpt_id from query parameter and store in session
        if ($request->has('gpt_id')) {
            session(['oauth_gpt_id' => $request->get('gpt_id')]);
        }

        return $next($request);
    }
}
