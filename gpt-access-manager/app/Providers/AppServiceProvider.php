<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Add middleware to Passport OAuth routes to capture gpt_id
        $this->app['router']->pushMiddlewareToGroup('web', \App\Http\Middleware\CaptureGptId::class);
    }
}
