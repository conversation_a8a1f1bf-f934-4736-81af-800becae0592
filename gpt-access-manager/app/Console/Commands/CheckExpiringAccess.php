<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckExpiringAccess extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'access:check-expiring';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for client GPT access expiring soon and send notifications.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for expiring access...');

        $expiringAccesses = \App\Models\ClientGptAccess::where('status', 'active')
            ->where('expired_at', '>', now())
            ->where('expired_at', '<=', now()->addDays(7))
            ->get();

        foreach ($expiringAccesses as $access) {
            $access->client->notify(new \App\Notifications\AccessExpiringSoon($access));
            $this->info("Notified client #{$access->client->id} about expiring access to GPT #{$access->gpt->id}.");
        }

        $this->info('Done.');
    }
}
