<?php

namespace App\Traits;

use App\Models\AuditLog;
use Illuminate\Database\Eloquent\Model;

trait Auditable
{
    public static function bootAuditable()
    {
        static::created(function (Model $model) {
            self::audit('created', $model);
        });

        static::updated(function (Model $model) {
            self::audit('updated', $model);
        });

        static::deleted(function (Model $model) {
            self::audit('deleted', $model);
        });
    }

    protected static function audit($action, $model)
    {
        AuditLog::create([
            'user_id' => auth()->id() ?? null,
            'action' => $action,
            'auditable_id' => $model->id,
            'auditable_type' => get_class($model),
            'old_values' => $action === 'updated' ? $model->getOriginal() : null,
            'new_values' => $action !== 'deleted' ? $model->getAttributes() : null,
            'user_agent' => request()->userAgent(),
            'ip_address' => request()->ip(),
        ]);
    }
}