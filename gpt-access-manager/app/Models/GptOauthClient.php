<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GptOauthClient extends Model
{
    use HasFactory;

    protected $fillable = [
        'gpt_id',
        'client_id',
        'client_secret',
    ];

    public function gpt(): BelongsTo
    {
        return $this->belongsTo(Gpt::class);
    }
}
