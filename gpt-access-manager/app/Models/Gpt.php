<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Gpt extends Model
{
    use HasFactory, Auditable;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'slug',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function clientGptAccesses(): HasMany
    {
        return $this->hasMany(ClientGptAccess::class);
    }

    public function oauthClient(): HasOne
    {
        return $this->hasOne(GptOauthClient::class);
    }
}
