@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
@section('breadcrumbs')
    {{ Breadcrumbs::render('gpts.show', $gpt) }}
@endsection

@section('content')
<div class="container mx-auto px-4 py-8">
    <div x-data="{ tab: '{{ request('tab', 'details') }}' }">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a href="#" @click.prevent="tab = 'details'" :class="{ 'border-indigo-500 text-indigo-600': tab === 'details', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': tab !== 'details' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Details
                </a>
                <a href="#" @click.prevent="tab = 'oauth'" :class="{ 'border-indigo-500 text-indigo-600': tab === 'oauth', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': tab !== 'oauth' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    OAuth Credentials
                </a>
            </nav>
        </div>

        <div x-show="tab === 'details'" class="mt-8">
            <div class="bg-white shadow-md rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h1 class="text-2xl font-bold text-gray-800">{{ $gpt->name }}</h1>
                    <a href="{{ route('gpts.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                        Back to List
                    </a>
                </div>

                <div class="mb-4">
                    <p class="text-gray-600"><strong>Description:</strong></p>
                    <p class="text-gray-800">{{ $gpt->description ?: 'No description provided.' }}</p>
                </div>

                <div class="mb-4">
                    <p class="text-gray-600"><strong>Slug:</strong></p>
                    <p class="text-gray-800">{{ $gpt->slug }}</p>
                </div>

                <div class="mb-4">
                    <p class="text-gray-600"><strong>Created At:</strong></p>
                    <p class="text-gray-800">{{ $gpt->created_at->format('F j, Y, g:i a') }}</p>
                </div>

                <div class="flex items-center space-x-4 mt-6">
                    <a href="{{ route('gpts.edit', $gpt->id) }}" class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded">
                        Edit
                    </a>
                    <form action="{{ route('gpts.destroy', $gpt->id) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this GPT?');">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">
                            Delete
                        </button>
                    </form>
                </div>

                <div class="mt-8 bg-white shadow-md rounded-lg">
                    <div class="p-6">
                        <h2 class="text-xl font-bold text-gray-800">API Usage (Last 7 Days)</h2>
                        <div class="mt-4">
                            <canvas id="usageChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="mt-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Clients with Access</h2>
                    @if($gpt->clientGptAccesses->count() > 0)
                        <div class="bg-white shadow-md rounded my-6">
                            <table class="min-w-full table-auto">
                                <thead class="bg-gray-200">
                                    <tr>
                                        <th class="px-6 py-3 border-b-2 border-gray-300 text-left text-xs leading-4 font-medium text-gray-600 uppercase tracking-wider">Client Name</th>
                                        <th class="px-6 py-3 border-b-2 border-gray-300 text-left text-xs leading-4 font-medium text-gray-600 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 border-b-2 border-gray-300 text-left text-xs leading-4 font-medium text-gray-600 uppercase tracking-wider">Expires At</th>
                                        <th class="px-6 py-3 border-b-2 border-gray-300"></th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white">
                                    @foreach ($gpt->clientGptAccesses as $access)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">{{ $access->client->name }}</td>
                                            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $access->status == 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                    {{ ucfirst($access->status) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">{{ $access->expired_at ? $access->expired_at->format('Y-m-d') : 'Never' }}</td>
                                            <td class="px-6 py-4 whitespace-no-wrap text-right border-b border-gray-200 text-sm leading-5 font-medium">
                                                <a href="{{ route('client-gpt-access.edit', $access) }}" class="text-indigo-600 hover:text-indigo-900">Edit Access</a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-gray-600">No clients have access to this GPT yet.</p>
                    @endif
                </div>
            </div>
        </div>

        <div x-show="tab === 'oauth'" class="mt-8" x-cloak>
            <div class="bg-white shadow-md rounded-lg p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">OAuth 2.0 Credentials</h2>
                @if ($gpt->oauthClient)
                    <div id="oauth-credentials">
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2">Client ID</label>
                            <input id="client-id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-100" type="text" value="{{ $gpt->oauthClient->client_id }}" readonly>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2">Client Secret</label>
                            <input id="client-secret" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-100" type="text" value="{{ $gpt->oauthClient->client_secret }}" readonly>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2">Authorization URL</label>
                            <input id="auth-url" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-100" type="text" value="{{ url('/oauth/authorize?gpt_id=' . $gpt->id) }}" readonly>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2">Token URL</label>
                            <input id="token-url" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-100" type="text" value="{{ url('/oauth/token') }}" readonly>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2">Scope</label>
                            <input id="scope" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-100" type="text" value="gptaccess" readonly>
                        </div>
                    </div>
                    <button onclick="copyCredentials()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        📋 Copy All
                    </button>
                @else
                    <p class="text-gray-600">OAuth client not found for this GPT.</p>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('tabs', () => ({
            tab: '{{ request('tab', 'details') }}'
        }))
    })

    const ctx = document.getElementById('usageChart').getContext('2d');
    const usageChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: @json($chartLabels),
            datasets: [{
                label: 'API Calls',
                data: @json($chartData),
                backgroundColor: 'rgba(129, 140, 248, 0.2)',
                borderColor: 'rgba(129, 140, 248, 1)',
                borderWidth: 1,
                tension: 0.4
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    function copyCredentials() {
        const clientId = document.getElementById('client-id').value;
        const clientSecret = document.getElementById('client-secret').value;
        const authUrl = document.getElementById('auth-url').value;
        const tokenUrl = document.getElementById('token-url').value;
        const scope = document.getElementById('scope').value;

        const textToCopy = `Client ID: ${clientId}\nClient Secret: ${clientSecret}\nAuthorization URL: ${authUrl}\nToken URL: ${tokenUrl}\nScope: ${scope}`;

        navigator.clipboard.writeText(textToCopy).then(function() {
            alert('Credentials copied to clipboard!');
        }, function(err) {
            alert('Could not copy text: ', err);
        });
    }
</script>
@endpush
