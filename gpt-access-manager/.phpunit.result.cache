{"version": 1, "defects": {"Tests\\Unit\\Models\\GptTest::it_belongs_to_a_user": 8, "Tests\\Unit\\Models\\GptTest::it_has_many_client_gpt_accesses": 8, "Tests\\Feature\\Api\\ClientControllerTest::it_can_get_client_details_via_me_endpoint": 8, "Tests\\Feature\\Api\\ClientControllerTest::it_can_validate_access_with_a_valid_token": 8, "Tests\\Feature\\Api\\ClientControllerTest::it_denies_access_with_an_invalid_token": 8, "Tests\\Feature\\Api\\ClientControllerTest::it_denies_access_when_expired": 8}, "times": {"Tests\\Unit\\Models\\GptTest::it_belongs_to_a_user": 0.58, "Tests\\Unit\\Models\\GptTest::it_has_many_client_gpt_accesses": 0.071, "Tests\\Unit\\Models\\GptTest::it_uses_auditable_trait": 0, "Tests\\Unit\\Models\\ClientTest::it_belongs_to_a_user": 0.036, "Tests\\Unit\\Models\\ClientTest::it_has_many_client_gpt_accesses": 0.008, "Tests\\Unit\\Models\\ClientTest::it_uses_auditable_trait": 0.001, "Tests\\Unit\\Models\\ClientGptAccessTest::it_belongs_to_a_client": 0.036, "Tests\\Unit\\Models\\ClientGptAccessTest::it_belongs_to_a_gpt": 0.004, "Tests\\Unit\\Models\\ClientGptAccessTest::it_uses_auditable_trait": 0}}